import logging

import librosa
import numpy as np
import soundfile as sf
import pyloudnorm as pyln
import os

logging.basicConfig(level=logging.DEBUG)
logging.getLogger("numba").setLevel(logging.WARNING)

log = logging.getLogger(__name__)

def normalize_wav(input_path, output_path, target_lufs=-23.0, target_sr=48000):
    # 1. 读取音频（自动重采样）
    data, sr = librosa.load(input_path, sr=target_sr)  # sr=target_sr 会强制重采样

    # 2. loudness meter
    meter = pyln.Meter(target_sr)
    loudness = meter.integrated_loudness(data)
    print('wav文件处理before的响度:{} Loudness: {}'.format(input_path, loudness))

    # 3. 归一化到目标响度
    normalized_audio = pyln.normalize.loudness(data, loudness, target_lufs)
    # peak = np.max(np.abs(normalized_audio))
    # if peak > 1.0:
    #     normalized_audio = normalized_audio / peak * 0.99
    loudness = meter.integrated_loudness(normalized_audio)
    print('wav文件处理after的响度:{} Loudness: {}'.format(input_path, loudness))
    # 4. 保存为 wav
    sf.write(output_path, normalized_audio, target_sr)

def wav_loud_normalization():
    # 批量处理目录
    input_dir = "ref_audio"
    output_dir = "output/ref_audio2"
    os.makedirs(output_dir, exist_ok=True)

    for file in os.listdir(input_dir):
        if file.endswith(".wav"):
            print("开始处理:" + file)
            normalize_wav(
                os.path.join(input_dir, file),
                os.path.join(output_dir, file),
                target_lufs=-16,  # 或者 -16/-14，根据需求
                target_sr=16000
            )


def wav_loud_test():
    """响度测试"""
    input_path = "/home/<USER>/works/qt-spirit-api/logs/1756795411.wav"
    input_path = "/home/<USER>/works/qt-spirit-api/logs/1756793433.wav"
    input_path = "/home/<USER>/works_ai/qt-tts-cosyvoice/ref_audio/095622.wav"
    input_path = "/home/<USER>/works/qt-spirit-api/logs/20250903100337.wav"
    target_sr = 16000
    # 1. 读取音频（自动重采样）
    data, sr = librosa.load(input_path)  # sr=target_sr 会强制重采样
    log.info("data.shape:{}, sr:{}".format(data.shape, sr))
    # 2. loudness meter
    meter = pyln.Meter(target_sr)
    loudness = meter.integrated_loudness(data)
    print("loudness:", loudness)


if __name__ == '__main__':
    wav_loud_test()