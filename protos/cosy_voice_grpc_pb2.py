# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: protos/cosy_voice_grpc.proto
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import symbol_database as _symbol_database
from google.protobuf.internal import builder as _builder
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()




DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(b'\n\x1cprotos/cosy_voice_grpc.proto\x12\x0f\x63osy_voice_grpc\"H\n\x14InferenceZeroShotReq\x12\x0c\n\x04text\x18\x01 \x01(\t\x12\x14\n\x0cspeed_factor\x18\x02 \x01(\x02\x12\x0c\n\x04role\x18\x03 \x01(\t\"_\n\x14InferenceInstructReq\x12\x0c\n\x04text\x18\x01 \x01(\t\x12\x14\n\x0cspeed_factor\x18\x02 \x01(\x02\x12\x0c\n\x04role\x18\x03 \x01(\t\x12\x15\n\rinstruct_text\x18\x04 \x01(\t\"@\n\rInferenceResp\x12\x12\n\naudio_data\x18\x01 \x01(\x0c\x12\x0f\n\x07\x65rr_msg\x18\x02 \x01(\t\x12\n\n\x02sr\x18\x03 \x01(\x05\"E\n\x0fInferenceConfig\x12\x14\n\x0cspeed_factor\x18\x01 \x01(\x02\x12\x0c\n\x04role\x18\x02 \x01(\t\x12\x0e\n\x06stream\x18\x03 \x01(\x08\"g\n\x16InferenceZeroShotBiReq\x12\x0e\n\x04text\x18\x01 \x01(\tH\x00\x12\x32\n\x06\x63onfig\x18\x02 \x01(\x0b\x32 .cosy_voice_grpc.InferenceConfigH\x00\x42\t\n\x07payload2\xb2\x02\n\x10\x43osyVoiceService\x12\\\n\x11InferenceZeroShot\x12%.cosy_voice_grpc.InferenceZeroShotReq\x1a\x1e.cosy_voice_grpc.InferenceResp0\x01\x12\\\n\x11InferenceInstruct\x12%.cosy_voice_grpc.InferenceInstructReq\x1a\x1e.cosy_voice_grpc.InferenceResp0\x01\x12\x62\n\x13InferenceZeroShotBi\x12\'.cosy_voice_grpc.InferenceZeroShotBiReq\x1a\x1e.cosy_voice_grpc.InferenceResp(\x01\x30\x01\x62\x06proto3')

_globals = globals()
_builder.BuildMessageAndEnumDescriptors(DESCRIPTOR, _globals)
_builder.BuildTopDescriptorsAndMessages(DESCRIPTOR, 'protos.cosy_voice_grpc_pb2', _globals)
if _descriptor._USE_C_DESCRIPTORS == False:

  DESCRIPTOR._options = None
  _globals['_INFERENCEZEROSHOTREQ']._serialized_start=49
  _globals['_INFERENCEZEROSHOTREQ']._serialized_end=121
  _globals['_INFERENCEINSTRUCTREQ']._serialized_start=123
  _globals['_INFERENCEINSTRUCTREQ']._serialized_end=218
  _globals['_INFERENCERESP']._serialized_start=220
  _globals['_INFERENCERESP']._serialized_end=284
  _globals['_INFERENCECONFIG']._serialized_start=286
  _globals['_INFERENCECONFIG']._serialized_end=355
  _globals['_INFERENCEZEROSHOTBIREQ']._serialized_start=357
  _globals['_INFERENCEZEROSHOTBIREQ']._serialized_end=460
  _globals['_COSYVOICESERVICE']._serialized_start=463
  _globals['_COSYVOICESERVICE']._serialized_end=769
# @@protoc_insertion_point(module_scope)
