"""
@generated by mypy-protobuf.  Do not edit manually!
isort:skip_file
"""

import builtins
import google.protobuf.descriptor
import google.protobuf.message
import typing

DESCRIPTOR: google.protobuf.descriptor.FileDescriptor

@typing.final
class InferenceZeroShotReq(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    TEXT_FIELD_NUMBER: builtins.int
    SPEED_FACTOR_FIELD_NUMBER: builtins.int
    ROLE_FIELD_NUMBER: builtins.int
    text: builtins.str
    speed_factor: builtins.float
    role: builtins.str
    def __init__(
        self,
        *,
        text: builtins.str = ...,
        speed_factor: builtins.float = ...,
        role: builtins.str = ...,
    ) -> None: ...
    def ClearField(self, field_name: typing.Literal["role", b"role", "speed_factor", b"speed_factor", "text", b"text"]) -> None: ...

global___InferenceZeroShotReq = InferenceZeroShotReq

@typing.final
class InferenceInstructReq(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    TEXT_FIELD_NUMBER: builtins.int
    SPEED_FACTOR_FIELD_NUMBER: builtins.int
    ROLE_FIELD_NUMBER: builtins.int
    INSTRUCT_TEXT_FIELD_NUMBER: builtins.int
    text: builtins.str
    speed_factor: builtins.float
    role: builtins.str
    instruct_text: builtins.str
    def __init__(
        self,
        *,
        text: builtins.str = ...,
        speed_factor: builtins.float = ...,
        role: builtins.str = ...,
        instruct_text: builtins.str = ...,
    ) -> None: ...
    def ClearField(self, field_name: typing.Literal["instruct_text", b"instruct_text", "role", b"role", "speed_factor", b"speed_factor", "text", b"text"]) -> None: ...

global___InferenceInstructReq = InferenceInstructReq

@typing.final
class InferenceResp(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    AUDIO_DATA_FIELD_NUMBER: builtins.int
    ERR_MSG_FIELD_NUMBER: builtins.int
    SR_FIELD_NUMBER: builtins.int
    audio_data: builtins.bytes
    err_msg: builtins.str
    sr: builtins.int
    def __init__(
        self,
        *,
        audio_data: builtins.bytes = ...,
        err_msg: builtins.str = ...,
        sr: builtins.int = ...,
    ) -> None: ...
    def ClearField(self, field_name: typing.Literal["audio_data", b"audio_data", "err_msg", b"err_msg", "sr", b"sr"]) -> None: ...

global___InferenceResp = InferenceResp

@typing.final
class InferenceConfig(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    SPEED_FACTOR_FIELD_NUMBER: builtins.int
    ROLE_FIELD_NUMBER: builtins.int
    STREAM_FIELD_NUMBER: builtins.int
    speed_factor: builtins.float
    role: builtins.str
    stream: builtins.bool
    def __init__(
        self,
        *,
        speed_factor: builtins.float = ...,
        role: builtins.str = ...,
        stream: builtins.bool = ...,
    ) -> None: ...
    def ClearField(self, field_name: typing.Literal["role", b"role", "speed_factor", b"speed_factor", "stream", b"stream"]) -> None: ...

global___InferenceConfig = InferenceConfig

@typing.final
class InferenceZeroShotBiReq(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    TEXT_FIELD_NUMBER: builtins.int
    CONFIG_FIELD_NUMBER: builtins.int
    text: builtins.str
    @property
    def config(self) -> global___InferenceConfig: ...
    def __init__(
        self,
        *,
        text: builtins.str = ...,
        config: global___InferenceConfig | None = ...,
    ) -> None: ...
    def HasField(self, field_name: typing.Literal["config", b"config", "payload", b"payload", "text", b"text"]) -> builtins.bool: ...
    def ClearField(self, field_name: typing.Literal["config", b"config", "payload", b"payload", "text", b"text"]) -> None: ...
    def WhichOneof(self, oneof_group: typing.Literal["payload", b"payload"]) -> typing.Literal["text", "config"] | None: ...

global___InferenceZeroShotBiReq = InferenceZeroShotBiReq
