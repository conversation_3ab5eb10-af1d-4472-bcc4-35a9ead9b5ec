# Generated by the gRPC Python protocol compiler plugin. DO NOT EDIT!
"""Client and server classes corresponding to protobuf-defined services."""
import grpc

from protos import cosy_voice_grpc_pb2 as protos_dot_cosy__voice__grpc__pb2


class CosyVoiceServiceStub(object):
    """CosyVoice服务
    """

    def __init__(self, channel):
        """Constructor.

        Args:
            channel: A grpc.Channel.
        """
        self.InferenceZeroShot = channel.unary_stream(
                '/cosy_voice_grpc.CosyVoiceService/InferenceZeroShot',
                request_serializer=protos_dot_cosy__voice__grpc__pb2.InferenceZeroShotReq.SerializeToString,
                response_deserializer=protos_dot_cosy__voice__grpc__pb2.InferenceResp.FromString,
                )
        self.InferenceInstruct = channel.unary_stream(
                '/cosy_voice_grpc.CosyVoiceService/InferenceInstruct',
                request_serializer=protos_dot_cosy__voice__grpc__pb2.InferenceInstructReq.SerializeToString,
                response_deserializer=protos_dot_cosy__voice__grpc__pb2.InferenceResp.FromString,
                )
        self.InferenceZeroShotBi = channel.stream_stream(
                '/cosy_voice_grpc.CosyVoiceService/InferenceZeroShotBi',
                request_serializer=protos_dot_cosy__voice__grpc__pb2.InferenceZeroShotBiReq.SerializeToString,
                response_deserializer=protos_dot_cosy__voice__grpc__pb2.InferenceResp.FromString,
                )


class CosyVoiceServiceServicer(object):
    """CosyVoice服务
    """

    def InferenceZeroShot(self, request, context):
        """零样本推理
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def InferenceInstruct(self, request, context):
        """指令推理
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def InferenceZeroShotBi(self, request_iterator, context):
        """零样本推理+text generator
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')


def add_CosyVoiceServiceServicer_to_server(servicer, server):
    rpc_method_handlers = {
            'InferenceZeroShot': grpc.unary_stream_rpc_method_handler(
                    servicer.InferenceZeroShot,
                    request_deserializer=protos_dot_cosy__voice__grpc__pb2.InferenceZeroShotReq.FromString,
                    response_serializer=protos_dot_cosy__voice__grpc__pb2.InferenceResp.SerializeToString,
            ),
            'InferenceInstruct': grpc.unary_stream_rpc_method_handler(
                    servicer.InferenceInstruct,
                    request_deserializer=protos_dot_cosy__voice__grpc__pb2.InferenceInstructReq.FromString,
                    response_serializer=protos_dot_cosy__voice__grpc__pb2.InferenceResp.SerializeToString,
            ),
            'InferenceZeroShotBi': grpc.stream_stream_rpc_method_handler(
                    servicer.InferenceZeroShotBi,
                    request_deserializer=protos_dot_cosy__voice__grpc__pb2.InferenceZeroShotBiReq.FromString,
                    response_serializer=protos_dot_cosy__voice__grpc__pb2.InferenceResp.SerializeToString,
            ),
    }
    generic_handler = grpc.method_handlers_generic_handler(
            'cosy_voice_grpc.CosyVoiceService', rpc_method_handlers)
    server.add_generic_rpc_handlers((generic_handler,))


 # This class is part of an EXPERIMENTAL API.
class CosyVoiceService(object):
    """CosyVoice服务
    """

    @staticmethod
    def InferenceZeroShot(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_stream(request, target, '/cosy_voice_grpc.CosyVoiceService/InferenceZeroShot',
            protos_dot_cosy__voice__grpc__pb2.InferenceZeroShotReq.SerializeToString,
            protos_dot_cosy__voice__grpc__pb2.InferenceResp.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)

    @staticmethod
    def InferenceInstruct(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_stream(request, target, '/cosy_voice_grpc.CosyVoiceService/InferenceInstruct',
            protos_dot_cosy__voice__grpc__pb2.InferenceInstructReq.SerializeToString,
            protos_dot_cosy__voice__grpc__pb2.InferenceResp.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)

    @staticmethod
    def InferenceZeroShotBi(request_iterator,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.stream_stream(request_iterator, target, '/cosy_voice_grpc.CosyVoiceService/InferenceZeroShotBi',
            protos_dot_cosy__voice__grpc__pb2.InferenceZeroShotBiReq.SerializeToString,
            protos_dot_cosy__voice__grpc__pb2.InferenceResp.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)
