syntax = "proto3";

package cosy_voice_grpc;

// CosyVoice服务
service CosyVoiceService {
    // 零样本推理
    rpc InferenceZeroShot(InferenceZeroShotReq) returns (stream InferenceResp);

    //指令推理
    rpc InferenceInstruct(InferenceInstructReq) returns (stream InferenceResp);
}

message InferenceZeroShotReq {
  string text = 1;
  float speed_factor = 2;
  string role = 3;
}

message InferenceInstructReq {
  string text = 1;
  float speed_factor = 2;
  string role = 3;
  string instruct_text = 4;
}

message InferenceResp {
  bytes audio_data = 1;
  string err_msg = 2;
  int32 sr = 3;
}
