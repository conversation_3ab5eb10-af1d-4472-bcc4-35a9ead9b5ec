import sys
import time
import logging
import pyloudnorm as pyln
import librosa

sys.path.append('CosyVoice/third_party/Matcha-TTS')
sys.path.append('CosyVoice')
import torchaudio
import torch
from CosyVoice.cosyvoice.cli.cosyvoice import CosyVoice2
from CosyVoice.cosyvoice.utils.file_utils import load_wav
from CosyVoice.cosyvoice.utils.common import set_all_random_seed
from tqdm import tqdm

from vllm import ModelRegistry
from CosyVoice.cosyvoice.vllm.cosyvoice2 import CosyVoice2ForCausalLM
from pathlib import Path
ModelRegistry.register_model("CosyVoice2ForCausalLM", CosyVoice2ForCausalLM)


def text_generator1():
    yield '收到好友'
    yield '从远方寄来'
    yield '的生日礼物，'
    yield '那份意外'
    yield '的惊喜与'
    yield '深深的祝福'
    yield '让我心中充'
    yield '满了甜蜜'
    yield '的快乐，笑容'
    yield '如花儿般绽放。'

def text_generator2():
    yield '你对得'
    yield '起我吗？昨天'
    yield '刚给你买的'
    yield '玩具你居然'
    yield '给我弄丢'
    yield '了，你看我'
    yield '不打死你'

def text_generator3():
    yield '你对得起我吗？'
    yield '昨天刚给你买的手表'
    yield '你居然给我弄丢了，'
    yield '你看我不打死你'

def main():
    cosyvoice = CosyVoice2('CosyVoice/pretrained_models/CosyVoice2-0.5B', load_jit=True, load_trt=True, load_vllm=False,
                           fp16=True)
    prompt_speech_16k = load_wav('ref_audio/200005.wav', 16000)
    instruct_text = ''
    tts_text='收到好友从远方寄来的生日礼物，那份意外的惊喜与深深的祝福让我心中充满了甜蜜的快乐，笑容如花儿般绽放。', '希望你以后能够做的比我还好呦。'

    def text_generator():
        yield '你们这些混蛋都去哪里了，'
        yield '害我找了这么大半天？'

    for i in tqdm(range(5)):
        set_all_random_seed(i)
        all_speech = []
        for _, j in enumerate(cosyvoice.inference_zero_shot(
                tts_text,
                instruct_text, prompt_speech_16k, stream=False)):
            all_speech.append(j['tts_speech'])
        all_speech = torch.cat(all_speech, dim=1)
        torchaudio.save(f'output/inference_zero_shot_{i}.wav', all_speech, cosyvoice.sample_rate)


def test():
    cosyvoice = CosyVoice2('CosyVoice/pretrained_models/CosyVoice2-0.5B', load_jit=True, load_trt=True, fp16=True,
                           load_vllm=True)
    prompt_speech_16k = load_wav('ref_audio/200005.wav', 16000)

    content = "你们这些混蛋都去哪里了，害我找了这么大半天？"
    # content = "在他讲述那个荒诞故事的过程中，他突然[laughter]停下来，因为他自己也被逗笑了[laughter]。"
    for i, j in enumerate(cosyvoice.inference_instruct2(
            content,
            '很生气地说', prompt_speech_16k=prompt_speech_16k, stream=False)):
        print(f'data:{cosyvoice.sample_rate}')
        torchaudio.save('output/instruct_8.wav', j['tts_speech'], cosyvoice.sample_rate)


def test_stream():
    cosyvoice = CosyVoice2('CosyVoice/pretrained_models/CosyVoice2-0.5B', load_jit=True, load_trt=True, fp16=True,
                           load_vllm=False)
    prompt_speech_16k = load_wav('ref_audio/200005.wav', 16000)
    instruct_text = ''
    tts_text = "你们这些混蛋都去哪里了，害我找了这么大半天？"

    # content = "在他讲述那个荒诞故事的过程中，他突然[laughter]停下来，因为他自己也被逗笑了[laughter]。"

    def text_generator():
        yield '你们这些混蛋都去哪里了，'
        yield '害我找了这么大半天？'

    all_speech = []
    set_all_random_seed(10)
    for i, j in enumerate(cosyvoice.inference_instruct2(
            tts_text,
            instruct_text, prompt_speech_16k=prompt_speech_16k, stream=False)):
        print(f'data:{cosyvoice.sample_rate}')
        # 这里需要组装j['tts_speech']到all_speech
        all_speech.append(j['tts_speech'])
        print(f"shape:{j['tts_speech'].shape}")

    all_speech = torch.cat(all_speech, dim=1)
    torchaudio.save(f'output/instruct_steam4.wav', all_speech, cosyvoice.sample_rate)


def demo1():
    cosyvoice = CosyVoice2('CosyVoice/pretrained_models/CosyVoice2-0.5B', load_jit=False, load_trt=False, load_vllm=False, fp16=False)
    set_all_random_seed(1756782362)
    prompt_speech_16k = load_wav('ref_audio/200005.wav', 16000)
    prompt_text = '列林格勒的拉多加湖，那里的冰层即使在夏天也很厚实，带上装备，我们明天就出发'
    for i, j in enumerate(cosyvoice.inference_zero_shot(
            '收到好友从远方寄来的生日礼物，那份意外的惊喜与深深的祝福让我心中充满了甜蜜的快乐，笑容如花儿般绽放。',
            prompt_text, prompt_speech_16k, stream=False)):
        torchaudio.save('output/demo1_zero_shot_{}.wav'.format(i), j['tts_speech'], cosyvoice.sample_rate)

def demo2():
    cosyvoice = CosyVoice2('CosyVoice/pretrained_models/CosyVoice2-0.5B', load_jit=False, load_trt=False, load_vllm=False, fp16=False)
    set_all_random_seed(1756782362)
    prompt_speech_16k = load_wav('ref_audio/200005.wav', 16000)
    prompt_text = '列林格勒的拉多加湖，那里的冰层即使在夏天也很厚实，带上装备，我们明天就出发'
    # save zero_shot spk for future usage
    # assert cosyvoice.add_zero_shot_spk(prompt_text, prompt_speech_16k, 'spk_demo2') is True

    for i, j in enumerate(cosyvoice.inference_zero_shot(
            '收到好友从远方寄来的生日礼物，那份意外的惊喜与深深的祝福让我心中充满了甜蜜的快乐，笑容如花儿般绽放。', '', '',
            zero_shot_spk_id='spk_demo2', stream=False)):
        torchaudio.save('output/demo2_zero_shot_{}.wav'.format(i), j['tts_speech'], cosyvoice.sample_rate)

def demo3():
    cosyvoice = CosyVoice2('CosyVoice/pretrained_models/CosyVoice2-0.5B', load_jit=False, load_trt=False,
                           load_vllm=False, fp16=False)
    set_all_random_seed(1756782362)
    # fine grained control, for supported control, check cosyvoice/tokenizer/tokenizer.py#L248
    for i, j in enumerate(cosyvoice.inference_cross_lingual(
            '在他讲述那个荒诞故事的过程中，他突然[laughter]停下来，因为他自己也被逗笑了[laughter]。', '', 'spk_demo2', stream=False)):
        torchaudio.save('output/demo3_fine_grained_control_{}.wav'.format(i), j['tts_speech'], cosyvoice.sample_rate)


def demo4(stream=False):
    cosyvoice = CosyVoice2('CosyVoice/pretrained_models/CosyVoice2-0.5B', load_jit=False, load_trt=False,
                           load_vllm=False, fp16=False)
    set_all_random_seed(1756782362)
    role = 'xiaoxiao'
    if not stream:
        for i, j in enumerate(
                cosyvoice.inference_zero_shot(text_generator2(), '', '', role, stream=False)):
            torchaudio.save('output/demo4_3_zero_shot_{}.wav'.format(i), j['tts_speech'], cosyvoice.sample_rate)
    else:
        all_speech = []
        for i, j in enumerate(
                cosyvoice.inference_zero_shot(text_generator2(), '', '', role, stream=True)):
            all_speech.append(j['tts_speech'])

        all_speech = torch.cat(all_speech, dim=1)
        torchaudio.save(f'output/demo4_3_zero_shot_stream.wav', all_speech, cosyvoice.sample_rate)
def demo5(role):
    cosyvoice = CosyVoice2('CosyVoice/pretrained_models/CosyVoice2-0.5B', load_jit=False, load_trt=False,
                           load_vllm=False, fp16=False)

    spks = cosyvoice.list_available_spks()
    print("spks:", spks)
    if role in spks:
        return

    prompt_speech_16k = load_wav(f'ref_audio/{role}.wav', 16000)
    prompt_text = Path(f'ref_audio/{role}.txt').read_text(encoding="utf-8")
    # save zero_shot spk for future usage
    assert cosyvoice.add_zero_shot_spk(prompt_text, prompt_speech_16k, role) is True
    cosyvoice.save_spkinfo()
    print(f"保存说话信息成功:{role}")


def demo6():
    cosyvoice = CosyVoice2('CosyVoice/pretrained_models/CosyVoice2-0.5B', load_jit=False, load_trt=False,
                           load_vllm=False, fp16=False)
    set_all_random_seed(1756782362)
    prompt_speech_16k = load_wav('ref_audio/200005.wav', 16000)


    for i, j in enumerate(cosyvoice.inference_instruct2(
            text_generator3(),
            '', prompt_speech_16k, stream=False)):
        torchaudio.save('output/demo6_3_instruct_{}.wav'.format(i), j['tts_speech'], cosyvoice.sample_rate)


if __name__ == '__main__':
    """
    1.定义全局说话人音色
    2.使用tts text generator，但是该方法目前只对inference_zero_shot友好
    3.返回使用stream返回，可以提高速度
    4.inference_zero_shot的缺点就是没有语气，这个inference_instruct2做得到，但是inference_instruct2连text generator实现都有问题
    """
    # demo5('xiaoxiao')
    demo4(True)
