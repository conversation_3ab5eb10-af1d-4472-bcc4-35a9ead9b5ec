import sys
sys.path.append('CosyVoice/third_party/Matcha-TTS')
sys.path.append('CosyVoice')
import torchaudio

from CosyVoice.cosyvoice.cli.cosyvoice import CosyVoice2
from CosyVoice.cosyvoice.utils.file_utils import load_wav
from CosyVoice.cosyvoice.utils.common import set_all_random_seed
from tqdm import tqdm

from vllm import ModelRegistry
from CosyVoice.cosyvoice.vllm.cosyvoice2 import CosyVoice2ForCausalLM
ModelRegistry.register_model("CosyVoice2ForCausalLM", CosyVoice2ForCausalLM)


def main():
    cosyvoice = CosyVoice2('pretrained_models/CosyVoice2-0.5B', load_jit=True, load_trt=True, load_vllm=True, fp16=True)
    prompt_speech_16k = load_wav('./asset/zero_shot_prompt.wav', 16000)
    for i in tqdm(range(100)):
        set_all_random_seed(i)
        for _, _ in enumerate(cosyvoice.inference_zero_shot('收到好友从远方寄来的生日礼物，那份意外的惊喜与深深的祝福让我心中充满了甜蜜的快乐，笑容如花儿般绽放。', '希望你以后能够做的比我还好呦。', prompt_speech_16k, stream=False)):
            continue

def test():
    cosyvoice = CosyVoice2('CosyVoice/pretrained_models/CosyVoice2-0.5B', load_jit=True, load_trt=True, fp16=True, load_vllm=True)
    prompt_speech_16k = load_wav('ref_audio/200005.wav', 16000)

    content = "你们这些混蛋都去哪里了，害我找了这么大半天？"
    # content = "在他讲述那个荒诞故事的过程中，他突然[laughter]停下来，因为他自己也被逗笑了[laughter]。"
    for i, j in enumerate(cosyvoice.inference_instruct2(
            content,
            '很生气地说', prompt_speech_16k=prompt_speech_16k, stream=False)):
        print(f'data:{cosyvoice.sample_rate}')
        torchaudio.save('output/instruct_8.wav', j['tts_speech'], cosyvoice.sample_rate)


if __name__ == '__main__':
    test()
