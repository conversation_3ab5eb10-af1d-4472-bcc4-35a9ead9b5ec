name-template: "v$RESOLVED_VERSION"
tag-template: "v$RESOLVED_VERSION"

categories:
  - title: "🚀 Features"
    labels:
      - "feature"
      - "enhancement"
  - title: "🐛 Bug Fixes"
    labels:
      - "fix"
      - "bugfix"
      - "bug"
  - title: "🧹 Maintenance"
    labels:
      - "maintenance"
      - "dependencies"
      - "refactoring"
      - "cosmetic"
      - "chore"
  - title: "📝️ Documentation"
    labels:
      - "documentation"
      - "docs"

change-template: "- $TITLE @$AUTHOR (#$NUMBER)"
change-title-escapes: '\<*_&' # You can add # and @ to disable mentions

version-resolver:
  major:
    labels:
      - "major"
  minor:
    labels:
      - "minor"
  patch:
    labels:
      - "patch"
  default: patch

template: |
  ## Changes

  $CHANGES
