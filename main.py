import logging
from concurrent import futures
import grpc, socket, sys
import consul
from grpc_health.v1 import health, health_pb2_grpc, health_pb2

sys.path.append('CosyVoice/third_party/Matcha-TTS')
sys.path.append('CosyVoice')

from vllm import ModelRegistry
from CosyVoice.cosyvoice.vllm.cosyvoice2 import CosyVoice2ForCausalLM

ModelRegistry.register_model("CosyVoice2ForCausalLM", CosyVoice2ForCausalLM)

from grpc_handle.cosy_voice_grpc_handle import CosyVoiceGrpcServicer
from protos import cosy_voice_grpc_pb2_grpc

logging.basicConfig(level=logging.INFO,
                    format='[%(asctime)s] [%(levelname)s] [%(name)s] %(message)s',
                    datefmt='%Y-%m-%d %H:%M:%S'
                    )
log = logging.getLogger(__name__)


def get_local_ip():
    s = socket.socket(socket.AF_INET, socket.SOCK_DGRAM)
    try:
        # 这里的 IP 不需要真实可达，只是用来确定本机网卡
        s.connect(("*******", 80))
        ip = s.getsockname()[0]
    except Exception:
        ip = "127.0.0.1"
    finally:
        s.close()
    return ip


def serve():
    host = '0.0.0.0'
    port = 8086
    options = [
        ('grpc.keepalive_time_ms', 10000),  # 每 10s ping 客户端一次
        ('grpc.keepalive_timeout_ms', 5000),  # ping 超过 5s 没回应，认为断开
        ('grpc.http2.max_pings_without_data', 0),  # 允许没有流也 ping
    ]
    # 注册到consul
    local_ip = get_local_ip()
    c = consul.Consul(host='*************', port=8500)
    c.agent.service.register(
        name='tts-cosyvoice-service',
        service_id=f'cosyvoice-{local_ip}',
        address=local_ip,
        port=port,
        check=consul.Check.grpc(f"{local_ip}:{port}", interval='10s', deregister='10m')
    )
    # 启动grpc服务
    server = grpc.server(futures.ThreadPoolExecutor(max_workers=10), options=options)

    # 注册handle
    handle = CosyVoiceGrpcServicer()
    handle.ModelPreHot()
    cosy_voice_grpc_pb2_grpc.add_CosyVoiceServiceServicer_to_server(handle, server)

    # 注册health check
    health_handle = health.HealthServicer()
    health_pb2_grpc.add_HealthServicer_to_server(health_handle, server)
    health_handle.set("Greeter", health_pb2.HealthCheckResponse.SERVING)

    # 启动grpc服务
    server.add_insecure_port(f"{host}:{port}")
    server.start()
    log.info(f"CosyVoice GRPC Server started at {host}:{port}")
    server.wait_for_termination()


if __name__ == '__main__':
    serve()
