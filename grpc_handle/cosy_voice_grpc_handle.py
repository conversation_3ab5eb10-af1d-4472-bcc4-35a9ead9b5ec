from pathlib import Path
from typing import Iterator

import grpc
import os
import logging
import threading
import time
import numpy as np
import torch
import pyloudnorm as pyln
import torchaudio.functional as F
from CosyVoice.cosyvoice.cli.cosyvoice import CosyVoice2, CosyVoice
from CosyVoice.cosyvoice.utils.common import set_all_random_seed
from CosyVoice.cosyvoice.utils.file_utils import load_wav
from protos.cosy_voice_grpc_pb2 import InferenceInstructReq, InferenceZeroShotReq, InferenceResp, InferenceZeroShotBiReq
from protos.cosy_voice_grpc_pb2_grpc import CosyVoiceServiceServicer

log = logging.getLogger(__name__)
log.setLevel(logging.INFO)  # 只输出 INFO 及以上级别的日志

_ref_audio_cache = {}
_ref_audio_cache_lock = threading.Lock()
_ref_text_cache = {}
_ref_text_cache_lock = threading.Lock()


# 读取指导音频
def ref_audio_cached(path):
    with _ref_audio_cache_lock:
        if path not in _ref_audio_cache:
            if not os.path.isfile(path):
                raise FileNotFoundError(f"ref audio file not found：{path}")
            _ref_audio_cache[path] = load_wav(path, 16000)
        return _ref_audio_cache[path]


# 读取指导文案
def ref_text_cached(path):
    with _ref_text_cache_lock:
        if path not in _ref_text_cache:
            if not os.path.isfile(path):
                raise FileNotFoundError(f"ref audio file not found：{path}")
            with open(path, 'r', encoding='utf-8') as f:
                _ref_text_cache[path] = f.read()
        return _ref_text_cache[path]


def normalize_audio_to_pcm_bytes(audio_tensor: torch.Tensor,
                                 pcm_dtype=np.int16,
                                 target_lufs: float = -23.0,
                                 target_peak: float = 0.95,
                                 sample_rate: int = 16000,
                                 apply_highpass: bool = True,
                                 highpass_cutoff: float = 80.0) -> bytes:
    """
    将音频 tensor 归一化处理为 PCM 字节：
    1. 高通滤波去低频噪音（可选）
    2. 峰值归一化（避免 clipping）
    3. LUFS响度归一化（统一听感音量）
    """
    log.info("audio_tensor.shape:{}".format(audio_tensor.shape))
    if not isinstance(audio_tensor, torch.Tensor):
        raise ValueError("输入必须是 torch.Tensor")

    # 1. 转 float32 并清理 NaN/Inf
    audio = torch.nan_to_num(audio_tensor.float())

    # 2. 保证至少 2D [batch, samples]
    if audio.dim() == 1:
        audio = audio.unsqueeze(0)  # [1, N]

    # 3. 高通滤波
    if apply_highpass:
        audio = F.highpass_biquad(audio, sample_rate, highpass_cutoff)

    # 4. 转 numpy -> (samples, channels) 格式
    audio_np = audio.cpu().numpy().T  # [batch, samples] -> [samples, channels]

    # # 5. 峰值归一化
    # peak = np.max(np.abs(audio_np))
    # if peak > 0:
    #     audio_np = audio_np / peak * target_peak
    #
    # # 6. LUFS响度归一化
    # meter = pyln.Meter(sample_rate)
    # loudness = meter.integrated_loudness(audio_np)
    # audio_np = pyln.normalize.loudness(audio_np, loudness, target_lufs)
    #
    # # 7. 最终 clip
    # audio_np = np.clip(audio_np, -1.0, 1.0)

    # 8. 转 PCM
    if pcm_dtype == np.int16:
        pcm = (audio_np * 32767).astype(np.int16)
    elif pcm_dtype == np.int32:
        pcm = (audio_np * **********).astype(np.int32)
    elif pcm_dtype == np.float32:
        pcm = audio_np.astype(np.float32)
    else:
        raise ValueError(f"不支持的 PCM 类型: {pcm_dtype}")

    return pcm.tobytes()


def check_audio_quality(tensor: torch.Tensor, chunk_id: int = 0) -> dict:
    """
        高效检查音频质量，返回质量报告。
        单次遍历完成所有统计指标，减少计算开销。

        Args:
            tensor: 音频tensor，shape [N] 或 [C, N]
            chunk_id: 音频块ID（用于日志）

        Returns:
            dict: 包含质量指标的字典
        """
    if not isinstance(tensor, torch.Tensor):
        return {"error": "输入不是torch.Tensor"}

    # 转 float32
    audio = tensor.float()
    num_samples = audio.numel()
    if num_samples == 0:
        return {
            "chunk_id": chunk_id,
            "peak_amplitude": 0.0,
            "mean_amplitude": 0.0,
            "std_deviation": 0.0,
            "has_nan": False,
            "has_inf": False,
            "clipping_ratio": 0.0,
            "is_mostly_silent": True,
            "sample_count": 0,
            "warnings": ["音频为空"],
            "is_healthy": False
        }

    # 初始化统计值
    peak_val = 0.0
    sum_val = 0.0
    sum_sq = 0.0
    clipped_count = 0
    has_nan = False
    has_inf = False

    clipping_threshold = 0.99
    silence_threshold = 0.001

    # 一次遍历计算所有指标
    flat_audio = audio.view(-1)
    for v in flat_audio:
        val = v.item()
        if torch.isnan(v):
            has_nan = True
            val = 0.0
        elif torch.isinf(v):
            has_inf = True
            val = 0.0

        abs_val = abs(val)
        peak_val = max(peak_val, abs_val)
        sum_val += abs_val
        sum_sq += val * val
        if abs_val > clipping_threshold:
            clipped_count += 1

    mean_val = sum_val / num_samples
    std_val = (sum_sq / num_samples - mean_val ** 2) ** 0.5
    clipping_ratio = clipped_count / num_samples
    is_mostly_silent = mean_val < silence_threshold

    quality_report = {
        "chunk_id": chunk_id,
        "peak_amplitude": peak_val,
        "mean_amplitude": mean_val,
        "std_deviation": std_val,
        "has_nan": has_nan,
        "has_inf": has_inf,
        "clipping_ratio": clipping_ratio,
        "is_mostly_silent": is_mostly_silent,
        "sample_count": num_samples
    }

    # 生成警告
    warnings = []
    if peak_val > 1.0:
        warnings.append(f"音频峰值超出范围: {peak_val:.3f}")
    if clipping_ratio > 0.01:
        warnings.append(f"检测到削波: {clipping_ratio * 100:.1f}%的样本")
    if has_nan or has_inf:
        warnings.append("检测到NaN或Inf值")
    if is_mostly_silent:
        warnings.append(f"音频信号过弱: 平均幅度 {mean_val:.6f}")

    quality_report["warnings"] = warnings
    quality_report["is_healthy"] = len(warnings) == 0

    return quality_report


def analyze_audio_processing_chain(original_tensor: torch.Tensor, processed_bytes: bytes,
                                   dtype: str = 'int16', chunk_id: int = 0) -> dict:
    """
    分析音频处理链的效果，比较原始音频和处理后音频的质量

    Args:
        original_tensor: 原始音频tensor
        processed_bytes: 处理后的PCM字节数据
        dtype: PCM数据类型
        chunk_id: 音频块ID

    Returns:
        dict: 处理效果分析报告
    """
    # 将处理后的字节数据转换回tensor进行分析
    if dtype == 'int16':
        processed_array = np.frombuffer(processed_bytes, dtype=np.int16).astype(np.float32) / 32767.0
    elif dtype == 'int32':
        processed_array = np.frombuffer(processed_bytes, dtype=np.int32).astype(np.float32) / **********.0
    elif dtype == 'float32':
        processed_array = np.frombuffer(processed_bytes, dtype=np.float32)
    else:
        return {"error": f"不支持的数据类型: {dtype}"}

    processed_tensor = torch.from_numpy(processed_array).reshape(original_tensor.shape)

    # 分析原始音频
    orig_peak = torch.max(torch.abs(original_tensor.to(torch.float32))).item()
    orig_rms = torch.sqrt(torch.mean(original_tensor.to(torch.float32) ** 2)).item()

    # 分析处理后音频
    proc_peak = torch.max(torch.abs(processed_tensor)).item()
    proc_rms = torch.sqrt(torch.mean(processed_tensor ** 2)).item()

    # 计算处理效果
    peak_change_db = 20 * np.log10(proc_peak / orig_peak) if orig_peak > 0 else 0
    rms_change_db = 20 * np.log10(proc_rms / orig_rms) if orig_rms > 0 else 0

    analysis = {
        "chunk_id": chunk_id,
        "original": {
            "peak": orig_peak,
            "rms": orig_rms,
            "peak_db": 20 * np.log10(orig_peak) if orig_peak > 0 else -np.inf
        },
        "processed": {
            "peak": proc_peak,
            "rms": proc_rms,
            "peak_db": 20 * np.log10(proc_peak) if proc_peak > 0 else -np.inf
        },
        "changes": {
            "peak_change_db": peak_change_db,
            "rms_change_db": rms_change_db,
            "dynamic_range_preserved": abs(peak_change_db - rms_change_db) < 1.0  # 动态范围是否保持
        }
    }

    return analysis


def CosyvoiceZeroShot(cosyvoice: CosyVoice, tts_text, spk, stream=False, speed=1.0):
    for i, j in enumerate(cosyvoice.inference_zero_shot(
            tts_text, '', '',
            zero_shot_spk_id=spk, stream=stream, speed=speed)):

        # 检查原始音频质量
        if log.isEnabledFor(logging.DEBUG):
            quality_report = check_audio_quality(j['tts_speech'], chunk_id=i)
            if not quality_report["is_healthy"]:
                log.warning(f"音频块 {i} 质量问题: {quality_report['warnings']}")

        # 专业TTS音频处理：归一化 + 低频噪音抑制
        print(j['tts_speech'][0][-100:])
        audio_bytes = normalize_audio_to_pcm_bytes(j['tts_speech'], target_lufs=-16,
                                                   sample_rate=cosyvoice.sample_rate)

        # 可选：分析音频处理效果（仅在debug级别日志时启用）
        if log.isEnabledFor(logging.DEBUG):
            analysis = analyze_audio_processing_chain(j['tts_speech'], audio_bytes, 'int16', i)
            log.debug(f"ZeroShot音频处理分析 块{i}: 峰值变化 {analysis['changes']['peak_change_db']:.1f}dB, "
                      f"RMS变化 {analysis['changes']['rms_change_db']:.1f}dB")
        yield InferenceResp(audio_data=audio_bytes, sr=cosyvoice.sample_rate)


class CosyVoiceGrpcServicer(CosyVoiceServiceServicer):

    def __init__(self):
        super().__init__()
        set_all_random_seed(1756782362)
        self.cosyvoice = CosyVoice2(
            model_dir='CosyVoice/pretrained_models/CosyVoice2-0.5B',
            load_jit=True,
            load_trt=True,
            fp16=True,
            load_vllm=False,
            trt_concurrent=4
        )

    def get_spk_by_role(self, role: str):
        available_spks = self.cosyvoice.list_available_spks()
        if role not in available_spks:
            log.info(f"添加新的spks:{role},已保存的spks:{available_spks}")
            prompt_speech_16k = load_wav(f'ref_audio/{role}.wav', 16000)
            prompt_text = Path(f'ref_audio/{role}.txt').read_text(encoding="utf-8")
            # save zero_shot spk for future usage
            assert self.cosyvoice.add_zero_shot_spk(prompt_text, prompt_speech_16k, role) is True
        return role

    def ModelPreHot(self):
        """模型预热"""
        request = InferenceZeroShotReq(role="172946", text="这是真爱吗，每晚面对一个头发花白满嘴白毛的糟老头，谁受得了。",
                                       speed_factor=1.0)
        # 检查示例音频
        ref_audio_path = f'ref_audio/{request.role}.wav'
        try:
            ref_audio = ref_audio_cached(ref_audio_path)
        except Exception as e:
            return InferenceResp(err_msg=str(e))
        # 检查示例音频文本内容
        ref_prompt_path = f'ref_audio/{request.role}.txt'
        try:
            ref_prompt = ref_text_cached(ref_prompt_path)
        except Exception as e:
            return InferenceResp(err_msg=str(e))
        log.debug(f"CosyVoice 准备推理:role:{request.role}, ref_prompt:{ref_prompt}, ref_audio_path:{ref_audio_path}")
        # 执行推理
        for i, audio_output in enumerate(
                self.cosyvoice.inference_zero_shot(request.text, ref_prompt, ref_audio, stream=True,
                                                   speed=request.speed_factor)):
            log.info(f"模型预热:chunk_{i}, sample_rate:{self.cosyvoice.sample_rate}")
            # 预热时也检查音频质量
            if 'tts_speech' in audio_output:
                max_val = torch.max(torch.abs(audio_output['tts_speech']))
                log.debug(f"预热音频块 {i} 峰值: {max_val:.3f}")

    def InferenceZeroShot(self, request: InferenceZeroShotReq, context: grpc.ServicerContext):
        start = time.time()
        try:
            # 检查示例音频
            ref_audio_path = f'ref_audio/{request.role}.wav'
            try:
                ref_audio = ref_audio_cached(ref_audio_path)
            except Exception as e:
                return InferenceResp(err_msg=str(e))
            # 检查示例音频文本内容
            ref_prompt_path = f'ref_audio/{request.role}.txt'
            try:
                ref_prompt = ref_text_cached(ref_prompt_path)
            except Exception as e:
                return InferenceResp(err_msg=str(e))
            log.debug(
                f"CosyVoice 准备推理:role:{request.role}, req_text:{request.text}, ref_audio_path:{ref_audio_path}")

            # 执行推理
            for i, j in enumerate(self.cosyvoice.inference_zero_shot(request.text, ref_prompt, ref_audio, stream=True,
                                                                     speed=request.speed_factor)):
                if not context.is_active():
                    log.info("CosyVoice 推理中断，本次执行结束")
                    break

                # 检查原始音频质量
                quality_report = check_audio_quality(j['tts_speech'], chunk_id=i)
                if not quality_report["is_healthy"]:
                    log.warning(f"音频块 {i} 质量问题: {quality_report['warnings']}")

                # 专业TTS音频处理：归一化 + 低频噪音抑制
                audio_bytes = normalize_audio_to_pcm_bytes(j['tts_speech'], target_lufs=-16,
                                                           sample_rate=self.cosyvoice.sample_rate)

                # 可选：分析音频处理效果（仅在debug级别日志时启用）
                # if log.isEnabledFor(logging.DEBUG):
                #     analysis = analyze_audio_processing_chain(j['tts_speech'], audio_bytes, 'int16', i)
                #     log.debug(f"ZeroShot音频处理分析 块{i}: 峰值变化 {analysis['changes']['peak_change_db']:.1f}dB, "
                #              f"RMS变化 {analysis['changes']['rms_change_db']:.1f}dB")

                yield InferenceResp(audio_data=audio_bytes, sr=self.cosyvoice.sample_rate)
        finally:
            end = time.time()
            log.info('CosyVoice 推理结束，耗时 {}'.format(end - start))

    def InferenceInstruct(self, request: InferenceInstructReq, context: grpc.ServicerContext):
        start = time.time()
        try:
            # 检查示例音频
            ref_audio_path = f'ref_audio/{request.role}.wav'
            try:
                ref_audio = ref_audio_cached(ref_audio_path)
            except Exception as e:
                return InferenceResp(err_msg=str(e))
            log.info(
                f"CosyVoice Instruct 准备推理:role:{request.role}, req_text:{request.text}, instruct_text:{request.instruct_text}, ref_audio_path:{ref_audio_path}")

            # 执行推理
            for i, j in enumerate(
                    self.cosyvoice.inference_instruct2(request.text, request.instruct_text, ref_audio, stream=True,
                                                       speed=request.speed_factor)):
                if not context.is_active():
                    log.info("CosyVoice Instruct 推理中断，本次执行结束")
                    break

                # 检查原始音频质量
                quality_report = check_audio_quality(j['tts_speech'], chunk_id=i)
                if not quality_report["is_healthy"]:
                    log.warning(f"Instruct音频块 {i} 质量问题: {quality_report['warnings']}")

                # 专业TTS音频处理：归一化 + 低频噪音抑制
                audio_bytes = normalize_audio_to_pcm_bytes(j['tts_speech'], target_lufs=-16,
                                                           sample_rate=self.cosyvoice.sample_rate)
                # 可选：分析音频处理效果（仅在debug级别日志时启用）
                if log.isEnabledFor(logging.DEBUG):
                    analysis = analyze_audio_processing_chain(j['tts_speech'], audio_bytes, 'int16', i)
                    log.debug(f"Instruct音频处理分析 块{i}: 峰值变化 {analysis['changes']['peak_change_db']:.1f}dB, "
                              f"RMS变化 {analysis['changes']['rms_change_db']:.1f}dB")

                yield InferenceResp(audio_data=audio_bytes, sr=self.cosyvoice.sample_rate)
        finally:
            end = time.time()
            log.info('CosyVoice Instruct 推理结束，耗时 {}'.format(end - start))

    def InferenceZeroShotBi(self, request_iterator: Iterator[InferenceZeroShotBiReq], context: grpc.ServicerContext):
        speed = 1.0
        spk = self.get_spk_by_role('xiaoxiao')
        stream = True
        start = time.time()
        try:
            # 执行推理
            for req in request_iterator:
                if req.HasField("config"):
                    speed = req.config.speed_factor if req.config.speed_factor > 0 else speed
                    spk = self.get_spk_by_role(req.config.role) if req.config.role != 'xiaoxiao' else spk
                    stream = req.config.stream
                    log.info(f"InferenceZeroShotBi 推理配置：spk：{spk}, speed:{speed}, stream:{stream}")
                if req.HasField("text"):
                    log.info(f"InferenceZeroShotBi 推理文本：text：{req.text}")
                    for i, resp in enumerate(CosyvoiceZeroShot(self.cosyvoice, req.text, spk, stream, speed)):
                        if not context.is_active():
                            log.info("InferenceZeroShotBi 推理中断，本次执行结束")
                            break
                        log.info(f'InferenceZeroShotBi 第{i}音频生成耗时：{time.time() - start}')
                        yield resp
        except Exception:
            log.exception("InferenceZeroShotBi 推理异常")
        finally:
            end = time.time()
            log.info('InferenceZeroShotBi 推理结束，耗时 {}'.format(end - start))
