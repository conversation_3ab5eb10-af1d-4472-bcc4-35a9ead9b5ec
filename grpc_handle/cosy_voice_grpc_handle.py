import grpc
import os
import logging
import threading
import time
import numpy as np
import torch

from CosyVoice.cosyvoice.cli.cosyvoice import CosyVoice2
from CosyVoice.cosyvoice.utils.file_utils import load_wav
from protos.cosy_voice_grpc_pb2 import InferenceInstructReq, InferenceZeroShotReq, InferenceResp
from protos.cosy_voice_grpc_pb2_grpc import CosyVoiceServiceServicer

log = logging.getLogger(__name__)

_ref_audio_cache = {}
_ref_audio_cache_lock = threading.Lock()
_ref_text_cache = {}
_ref_text_cache_lock = threading.Lock()


# 读取指导音频
def ref_audio_cached(path):
    with _ref_audio_cache_lock:
        if path not in _ref_audio_cache:
            if not os.path.isfile(path):
                raise FileNotFoundError(f"ref audio file not found：{path}")
            _ref_audio_cache[path] = load_wav(path, 16000)
        return _ref_audio_cache[path]


# 读取指导文案
def ref_text_cached(path):
    with _ref_text_cache_lock:
        if path not in _ref_text_cache:
            if not os.path.isfile(path):
                raise FileNotFoundError(f"ref audio file not found：{path}")
            with open(path, 'r', encoding='utf-8') as f:
                _ref_text_cache[path] = f.read()
        return _ref_text_cache[path]


dtype_map = {
    'int16': np.int16,
    'int32': np.int32,
    'float32': np.float32,
}


def tensor_to_pcm_bytes(tensor: torch.Tensor, dtype='int16') -> bytes:
    if dtype_map[dtype] == tensor.dtype:
        return tensor.numpy().tobytes()
    if dtype == 'int16':
        data = (tensor.clamp(-1, 1).numpy() * 32767).astype(np.int16)
    elif dtype == 'int32':
        data = (tensor.clamp(-1, 1).numpy() * 2147483647).astype(np.int32)
    elif dtype == 'float32':
        data = tensor.numpy().astype(np.float32)
    else:
        raise ValueError(f"Unsupported dtype: {dtype}")

    return data.tobytes()


def adjust_pcm_volume_torch(pcm: torch.Tensor, gain: float = 1.0, normalize: bool = True) -> torch.Tensor:
    """
    调整 PCM 音量，支持自动归一化，兼容 torch.Tensor。

    参数:
        pcm       : torch.Tensor, PCM 音频数据 (int16 或 float32/float64)
        gain      : float, 音量倍数，例如 2.0 放大 2 倍，0.5 缩小一半
        normalize : bool, 是否自动归一化到接近最大幅度

    返回:
        torch.Tensor, 调整音量后的 PCM 数据，dtype 与输入一致
    """
    if not isinstance(pcm, torch.Tensor):
        raise ValueError("pcm 必须是 torch.Tensor")

    orig_dtype = pcm.dtype
    pcm_adj = pcm.to(torch.float64)  # 统一转 float64 计算

    # 自动归一化
    if normalize:
        max_val = torch.max(torch.abs(pcm_adj))
        if max_val > 0:
            pcm_adj = pcm_adj / max_val

    # 应用增益
    pcm_adj = pcm_adj * gain

    # 根据原始 dtype 限制范围
    if orig_dtype in [torch.int16]:
        pcm_adj = torch.clamp(pcm_adj, -1.0, 1.0)
        pcm_adj = (pcm_adj * 32767).to(torch.int16)
    elif orig_dtype in [torch.float32, torch.float64]:
        pcm_adj = torch.clamp(pcm_adj, -1.0, 1.0)
        pcm_adj = pcm_adj.to(orig_dtype)
    else:
        raise ValueError(f"暂不支持的 PCM dtype: {orig_dtype}")

    return pcm_adj


class CosyVoiceGrpcServicer(CosyVoiceServiceServicer):

    def __init__(self):
        super().__init__()
        self.cosyvoice = CosyVoice2(
            model_dir='CosyVoice/pretrained_models/CosyVoice2-0.5B',
            load_jit=True,
            load_trt=True,
            fp16=True,
            load_vllm=True,
            trt_concurrent=4
        )

    def ModelPreHot(self):
        """模型预热"""
        request = InferenceZeroShotReq(role="172946", text="这是真爱吗，每晚面对一个头发花白满嘴白毛的糟老头，谁受得了。",
                                       speed_factor=1.0)
        # 检查示例音频
        ref_audio_path = f'ref_audio/{request.role}.wav'
        try:
            ref_audio = ref_audio_cached(ref_audio_path)
        except Exception as e:
            return InferenceResp(err_msg=str(e))
        # 检查示例音频文本内容
        ref_prompt_path = f'ref_audio/{request.role}.txt'
        try:
            ref_prompt = ref_text_cached(ref_prompt_path)
        except Exception as e:
            return InferenceResp(err_msg=str(e))
        log.debug(f"CosyVoice 准备推理:role:{request.role}, ref_prompt:{ref_prompt}, ref_audio_path:{ref_audio_path}")
        # 执行推理
        for i, j in enumerate(self.cosyvoice.inference_zero_shot(request.text, ref_prompt, ref_audio, stream=True,
                                                                 speed=request.speed_factor)):
            log.info(f"模型预热:i:{i}, sample_rate:{self.cosyvoice.sample_rate}")

    def InferenceZeroShot(self, request: InferenceZeroShotReq, context: grpc.ServicerContext):
        start = time.time()
        try:
            # 检查示例音频
            ref_audio_path = f'ref_audio/{request.role}.wav'
            try:
                ref_audio = ref_audio_cached(ref_audio_path)
            except Exception as e:
                return InferenceResp(err_msg=str(e))
            # 检查示例音频文本内容
            ref_prompt_path = f'ref_audio/{request.role}.txt'
            try:
                ref_prompt = ref_text_cached(ref_prompt_path)
            except Exception as e:
                return InferenceResp(err_msg=str(e))
            log.debug(
                f"CosyVoice 准备推理:role:{request.role}, req_text:{request.text}, ref_audio_path:{ref_audio_path}")

            # 执行推理
            for i, j in enumerate(self.cosyvoice.inference_zero_shot(request.text, ref_prompt, ref_audio, stream=True,
                                                                     speed=request.speed_factor)):
                if not context.is_active():
                    log.info("CosyVoice 推理中断，本次执行结束")
                    break
                yield InferenceResp(audio_data=tensor_to_pcm_bytes(j['tts_speech']), sr=self.cosyvoice.sample_rate)
        finally:
            end = time.time()
            log.info('CosyVoice 推理结束，耗时 {}'.format(end - start))

    def InferenceInstruct(self, request: InferenceInstructReq, context: grpc.ServicerContext):
        start = time.time()
        try:
            # 检查示例音频
            ref_audio_path = f'ref_audio/{request.role}.wav'
            try:
                ref_audio = ref_audio_cached(ref_audio_path)
            except Exception as e:
                return InferenceResp(err_msg=str(e))
            log.debug(
                f"CosyVoice Instruct 准备推理:role:{request.role}, req_text:{request.text}, instruct_text:{request.instruct_text}, ref_audio_path:{ref_audio_path}")

            # 执行推理
            for i, j in enumerate(
                    self.cosyvoice.inference_instruct2(request.text, request.instruct_text, ref_audio, stream=True,
                                                       speed=request.speed_factor)):
                if not context.is_active():
                    log.info("CosyVoice Instruct 推理中断，本次执行结束")
                    break
                tmp = adjust_pcm_volume_torch(j['tts_speech'])
                yield InferenceResp(audio_data=tensor_to_pcm_bytes(tmp), sr=self.cosyvoice.sample_rate)
        finally:
            end = time.time()
            log.info('CosyVoice Instruct 推理结束，耗时 {}'.format(end - start))
