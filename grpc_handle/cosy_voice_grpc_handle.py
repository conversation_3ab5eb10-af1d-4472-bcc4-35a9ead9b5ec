import grpc
import os
import logging
import threading
import time
import numpy as np
import torch

from CosyVoice.cosyvoice.cli.cosyvoice import CosyVoice2
from CosyVoice.cosyvoice.utils.file_utils import load_wav
from protos.cosy_voice_grpc_pb2 import InferenceInstructReq, InferenceZeroShotReq, InferenceResp
from protos.cosy_voice_grpc_pb2_grpc import CosyVoiceServiceServicer

log = logging.getLogger(__name__)

_ref_audio_cache = {}
_ref_audio_cache_lock = threading.Lock()
_ref_text_cache = {}
_ref_text_cache_lock = threading.Lock()


# 读取指导音频
def ref_audio_cached(path):
    with _ref_audio_cache_lock:
        if path not in _ref_audio_cache:
            if not os.path.isfile(path):
                raise FileNotFoundError(f"ref audio file not found：{path}")
            _ref_audio_cache[path] = load_wav(path, 16000)
        return _ref_audio_cache[path]


# 读取指导文案
def ref_text_cached(path):
    with _ref_text_cache_lock:
        if path not in _ref_text_cache:
            if not os.path.isfile(path):
                raise FileNotFoundError(f"ref audio file not found：{path}")
            with open(path, 'r', encoding='utf-8') as f:
                _ref_text_cache[path] = f.read()
        return _ref_text_cache[path]


dtype_map = {
    'int16': np.int16,
    'int32': np.int32,
    'float32': np.float32,
}


def process_audio_to_pcm_bytes(tensor: torch.Tensor, dtype='int16', gain: float = 1.0,
                              normalize: bool = True, normalize_threshold: float = 0.1) -> bytes:
    """
    一站式音频处理：安全地调整音量并转换为PCM字节数据，防止爆音和音频异常

    Args:
        tensor: 输入的音频tensor
        dtype: 目标PCM数据类型 ('int16', 'int32', 'float32')
        gain: 音量倍数，例如 2.0 放大 2 倍，0.5 缩小一半
        normalize: 是否自动归一化到接近最大幅度
        normalize_threshold: 归一化阈值，低于此值的信号不进行归一化（防止放大噪声）

    Returns:
        bytes: 处理后的PCM字节数据
    """
    if not isinstance(tensor, torch.Tensor):
        raise ValueError("输入必须是 torch.Tensor")

    # 统一转换为float32进行处理，确保精度
    audio = tensor.to(torch.float32)

    # 第一步：检查并清理异常值（NaN或Inf）
    if torch.isnan(audio).any() or torch.isinf(audio).any():
        log.error("检测到音频数据中有NaN或Inf值，进行清理")
        audio = torch.where(torch.isnan(audio) | torch.isinf(audio),
                           torch.zeros_like(audio), audio)

    # 第二步：智能归一化（只对足够强的信号进行归一化，避免放大噪声）
    if normalize:
        max_val = torch.max(torch.abs(audio))
        if max_val > normalize_threshold:
            # 使用软限制，避免完全归一化到1.0（留一些余量防止削波）
            target_level = 0.95
            audio = audio * (target_level / max_val)
            log.debug(f"音频归一化：原始峰值 {max_val:.3f} -> 目标峰值 {target_level}")
        else:
            log.debug(f"音频信号过弱 ({max_val:.3f} < {normalize_threshold})，跳过归一化")

    # 第三步：应用音量增益
    if gain != 1.0:
        audio = audio * gain
        log.debug(f"应用音量增益: {gain}")

    # 第四步：最终限幅保护，确保不会爆音
    final_max = torch.max(torch.abs(audio))
    if final_max > 1.0:
        log.warning(f"处理后音频幅度仍超出范围 [{final_max:.3f}]，进行最终限幅")
        audio = torch.clamp(audio, -1.0, 1.0)

    # 第五步：根据目标类型进行转换并返回字节数据
    if dtype == 'int16':
        # 转换为int16，范围[-32767, 32767]
        data = (audio.numpy() * 32767).astype(np.int16)
    elif dtype == 'int32':
        # 转换为int32，范围[-2147483647, 2147483647]
        data = (audio.numpy() * 2147483647).astype(np.int32)
    elif dtype == 'float32':
        # 保持float32格式
        data = audio.numpy().astype(np.float32)
    else:
        raise ValueError(f"不支持的数据类型: {dtype}")

    return data.tobytes()


def check_audio_quality(tensor: torch.Tensor, chunk_id: int = 0) -> dict:
    """
    检查音频质量，返回质量报告

    Args:
        tensor: 音频tensor
        chunk_id: 音频块ID（用于日志）

    Returns:
        dict: 包含质量指标的字典
    """
    if not isinstance(tensor, torch.Tensor):
        return {"error": "输入不是torch.Tensor"}

    # 转换为float32进行分析
    audio = tensor.to(torch.float32) if tensor.dtype != torch.float32 else tensor

    # 基本统计
    max_val = torch.max(torch.abs(audio)).item()
    mean_val = torch.mean(torch.abs(audio)).item()
    std_val = torch.std(audio).item()

    # 检查异常值
    has_nan = torch.isnan(audio).any().item()
    has_inf = torch.isinf(audio).any().item()

    # 检查削波（接近最大值的样本比例）
    clipping_threshold = 0.99
    clipped_samples = torch.sum(torch.abs(audio) > clipping_threshold).item()
    clipping_ratio = clipped_samples / audio.numel()

    # 检查静音（过低的信号）
    silence_threshold = 0.001
    is_mostly_silent = mean_val < silence_threshold

    quality_report = {
        "chunk_id": chunk_id,
        "peak_amplitude": max_val,
        "mean_amplitude": mean_val,
        "std_deviation": std_val,
        "has_nan": has_nan,
        "has_inf": has_inf,
        "clipping_ratio": clipping_ratio,
        "is_mostly_silent": is_mostly_silent,
        "sample_count": audio.numel()
    }

    # 质量警告
    warnings = []
    if max_val > 1.0:
        warnings.append(f"音频峰值超出范围: {max_val:.3f}")
    if clipping_ratio > 0.01:  # 超过1%的样本被削波
        warnings.append(f"检测到削波: {clipping_ratio*100:.1f}%的样本")
    if has_nan or has_inf:
        warnings.append("检测到NaN或Inf值")
    if is_mostly_silent:
        warnings.append(f"音频信号过弱: 平均幅度 {mean_val:.6f}")

    quality_report["warnings"] = warnings
    quality_report["is_healthy"] = len(warnings) == 0

    return quality_report


class CosyVoiceGrpcServicer(CosyVoiceServiceServicer):

    def __init__(self):
        super().__init__()
        self.cosyvoice = CosyVoice2(
            model_dir='CosyVoice/pretrained_models/CosyVoice2-0.5B',
            load_jit=True,
            load_trt=True,
            fp16=True,
            load_vllm=True,
            trt_concurrent=4
        )

    def ModelPreHot(self):
        """模型预热"""
        request = InferenceZeroShotReq(role="172946", text="这是真爱吗，每晚面对一个头发花白满嘴白毛的糟老头，谁受得了。",
                                       speed_factor=1.0)
        # 检查示例音频
        ref_audio_path = f'ref_audio/{request.role}.wav'
        try:
            ref_audio = ref_audio_cached(ref_audio_path)
        except Exception as e:
            return InferenceResp(err_msg=str(e))
        # 检查示例音频文本内容
        ref_prompt_path = f'ref_audio/{request.role}.txt'
        try:
            ref_prompt = ref_text_cached(ref_prompt_path)
        except Exception as e:
            return InferenceResp(err_msg=str(e))
        log.debug(f"CosyVoice 准备推理:role:{request.role}, ref_prompt:{ref_prompt}, ref_audio_path:{ref_audio_path}")
        # 执行推理
        for i, audio_output in enumerate(self.cosyvoice.inference_zero_shot(request.text, ref_prompt, ref_audio, stream=True,
                                                                 speed=request.speed_factor)):
            log.info(f"模型预热:chunk_{i}, sample_rate:{self.cosyvoice.sample_rate}")
            # 预热时也检查音频质量
            if 'tts_speech' in audio_output:
                max_val = torch.max(torch.abs(audio_output['tts_speech']))
                log.debug(f"预热音频块 {i} 峰值: {max_val:.3f}")

    def InferenceZeroShot(self, request: InferenceZeroShotReq, context: grpc.ServicerContext):
        start = time.time()
        try:
            # 检查示例音频
            ref_audio_path = f'ref_audio/{request.role}.wav'
            try:
                ref_audio = ref_audio_cached(ref_audio_path)
            except Exception as e:
                return InferenceResp(err_msg=str(e))
            # 检查示例音频文本内容
            ref_prompt_path = f'ref_audio/{request.role}.txt'
            try:
                ref_prompt = ref_text_cached(ref_prompt_path)
            except Exception as e:
                return InferenceResp(err_msg=str(e))
            log.debug(
                f"CosyVoice 准备推理:role:{request.role}, req_text:{request.text}, ref_audio_path:{ref_audio_path}")

            # 执行推理
            for i, j in enumerate(self.cosyvoice.inference_zero_shot(request.text, ref_prompt, ref_audio, stream=True,
                                                                     speed=request.speed_factor)):
                if not context.is_active():
                    log.info("CosyVoice 推理中断，本次执行结束")
                    break

                # 检查原始音频质量
                quality_report = check_audio_quality(j['tts_speech'], chunk_id=i)
                if not quality_report["is_healthy"]:
                    log.warning(f"音频块 {i} 质量问题: {quality_report['warnings']}")

                # 一站式音频处理：音量调整 + 转换为PCM字节数据
                audio_bytes = process_audio_to_pcm_bytes(j['tts_speech'], dtype='int16',
                                                        gain=1.0, normalize=True)
                yield InferenceResp(audio_data=audio_bytes, sr=self.cosyvoice.sample_rate)
        finally:
            end = time.time()
            log.info('CosyVoice 推理结束，耗时 {}'.format(end - start))

    def InferenceInstruct(self, request: InferenceInstructReq, context: grpc.ServicerContext):
        start = time.time()
        try:
            # 检查示例音频
            ref_audio_path = f'ref_audio/{request.role}.wav'
            try:
                ref_audio = ref_audio_cached(ref_audio_path)
            except Exception as e:
                return InferenceResp(err_msg=str(e))
            log.debug(
                f"CosyVoice Instruct 准备推理:role:{request.role}, req_text:{request.text}, instruct_text:{request.instruct_text}, ref_audio_path:{ref_audio_path}")

            # 执行推理
            for i, j in enumerate(
                    self.cosyvoice.inference_instruct2(request.text, request.instruct_text, ref_audio, stream=True,
                                                       speed=request.speed_factor)):
                if not context.is_active():
                    log.info("CosyVoice Instruct 推理中断，本次执行结束")
                    break

                # 检查原始音频质量
                quality_report = check_audio_quality(j['tts_speech'], chunk_id=i)
                if not quality_report["is_healthy"]:
                    log.warning(f"Instruct音频块 {i} 质量问题: {quality_report['warnings']}")

                # 一站式音频处理：音量调整 + 转换为PCM字节数据
                audio_bytes = process_audio_to_pcm_bytes(j['tts_speech'], dtype='int16',
                                                        gain=1.0, normalize=True)
                yield InferenceResp(audio_data=audio_bytes, sr=self.cosyvoice.sample_rate)
        finally:
            end = time.time()
            log.info('CosyVoice Instruct 推理结束，耗时 {}'.format(end - start))
