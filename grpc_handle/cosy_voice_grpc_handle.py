import grpc
import os
import logging
import threading
import time
import numpy as np
import torch

from CosyVoice.cosyvoice.cli.cosyvoice import CosyVoice2
from CosyVoice.cosyvoice.utils.file_utils import load_wav
from protos.cosy_voice_grpc_pb2 import InferenceInstructReq, InferenceZeroShotReq, InferenceResp
from protos.cosy_voice_grpc_pb2_grpc import CosyVoiceServiceServicer

log = logging.getLogger(__name__)

_ref_audio_cache = {}
_ref_audio_cache_lock = threading.Lock()
_ref_text_cache = {}
_ref_text_cache_lock = threading.Lock()


# 读取指导音频
def ref_audio_cached(path):
    with _ref_audio_cache_lock:
        if path not in _ref_audio_cache:
            if not os.path.isfile(path):
                raise FileNotFoundError(f"ref audio file not found：{path}")
            _ref_audio_cache[path] = load_wav(path, 16000)
        return _ref_audio_cache[path]


# 读取指导文案
def ref_text_cached(path):
    with _ref_text_cache_lock:
        if path not in _ref_text_cache:
            if not os.path.isfile(path):
                raise FileNotFoundError(f"ref audio file not found：{path}")
            with open(path, 'r', encoding='utf-8') as f:
                _ref_text_cache[path] = f.read()
        return _ref_text_cache[path]


dtype_map = {
    'int16': np.int16,
    'int32': np.int32,
    'float32': np.float32,
}


def process_audio_to_pcm_bytes(tensor: torch.Tensor, dtype='int16', gain: float = 1.0,
                              normalize: bool = True, normalize_threshold: float = 0.01,
                              target_lufs: float = -23.0, enable_soft_limiting: bool = True) -> bytes:
    """
    专业音频处理：优先使用归一化，避免硬限幅失真

    Args:
        tensor: 输入的音频tensor
        dtype: 目标PCM数据类型 ('int16', 'int32', 'float32')
        gain: 音量倍数，例如 2.0 放大 2 倍，0.5 缩小一半
        normalize: 是否进行峰值归一化
        normalize_threshold: 归一化阈值，低于此值的信号不进行归一化（防止放大噪声）
        target_lufs: 目标响度（LUFS），用于响度归一化（暂未实现，预留接口）
        enable_soft_limiting: 是否启用软限制器（仅在必要时使用）

    Returns:
        bytes: 处理后的PCM字节数据
    """
    if not isinstance(tensor, torch.Tensor):
        raise ValueError("输入必须是 torch.Tensor")

    # 统一转换为float64进行处理，确保高精度计算
    audio = tensor.to(torch.float64)

    # 第一步：检查并清理异常值（NaN或Inf）
    if torch.isnan(audio).any() or torch.isinf(audio).any():
        log.error("检测到音频数据中有NaN或Inf值，进行清理")
        audio = torch.where(torch.isnan(audio) | torch.isinf(audio),
                           torch.zeros_like(audio), audio)

    # 第二步：应用音量增益（在归一化之前）
    if gain != 1.0:
        audio = audio * gain
        log.debug(f"应用音量增益: {gain}")

    # 第三步：智能峰值归一化（避免硬限幅）
    max_val = torch.max(torch.abs(audio))

    if normalize and max_val > normalize_threshold:
        if max_val > 1.0:
            # 需要归一化以避免削波
            # 使用稍微保守的目标电平，为后续处理留出余量
            target_peak = 0.95
            normalization_factor = target_peak / max_val
            audio = audio * normalization_factor
            log.info(f"峰值归一化：{max_val:.3f} -> {target_peak:.3f} (因子: {normalization_factor:.3f})")
        else:
            # 信号电平合适，可选择性地进行轻微归一化以获得更好的动态范围利用
            if max_val < 0.5:  # 信号较弱时进行适度提升
                target_peak = 0.8
                normalization_factor = target_peak / max_val
                audio = audio * normalization_factor
                log.debug(f"信号提升：{max_val:.3f} -> {target_peak:.3f} (因子: {normalization_factor:.3f})")
    elif max_val <= normalize_threshold:
        log.debug(f"音频信号过弱 ({max_val:.6f} <= {normalize_threshold})，跳过归一化")

    # 第四步：软限制器（仅在绝对必要时使用，避免硬限幅失真）
    final_max = torch.max(torch.abs(audio))
    if final_max > 1.0:
        if enable_soft_limiting:
            # 使用tanh软限制器，比硬限幅产生更少的谐波失真
            log.warning(f"音频峰值 {final_max:.3f} 超出范围，使用软限制器")
            # tanh软限制：渐进地接近±1，但永远不会超过
            audio = torch.tanh(audio * 0.9)  # 0.9系数使软限制更温和
        else:
            # 如果禁用软限制，则进行紧急归一化（比硬限幅好）
            log.warning(f"音频峰值 {final_max:.3f} 超出范围，进行紧急归一化")
            audio = audio / final_max * 0.95

    # 第五步：转换为目标数据类型
    # 转换为float32以减少精度损失，然后进行最终转换
    audio = audio.to(torch.float32)

    if dtype == 'int16':
        # 转换为int16，范围[-32767, 32767]
        # 使用32767而不是32768以避免溢出
        data = (audio.numpy() * 32767).astype(np.int16)
    elif dtype == 'int32':
        # 转换为int32，范围[-**********, **********]
        data = (audio.numpy() * **********).astype(np.int32)
    elif dtype == 'float32':
        # 保持float32格式
        data = audio.numpy().astype(np.float32)
    else:
        raise ValueError(f"不支持的数据类型: {dtype}")

    return data.tobytes()


def check_audio_quality(tensor: torch.Tensor, chunk_id: int = 0) -> dict:
    """
    检查音频质量，返回质量报告

    Args:
        tensor: 音频tensor
        chunk_id: 音频块ID（用于日志）

    Returns:
        dict: 包含质量指标的字典
    """
    if not isinstance(tensor, torch.Tensor):
        return {"error": "输入不是torch.Tensor"}

    # 转换为float32进行分析
    audio = tensor.to(torch.float32) if tensor.dtype != torch.float32 else tensor

    # 基本统计
    max_val = torch.max(torch.abs(audio)).item()
    mean_val = torch.mean(torch.abs(audio)).item()
    std_val = torch.std(audio).item()

    # 检查异常值
    has_nan = torch.isnan(audio).any().item()
    has_inf = torch.isinf(audio).any().item()

    # 检查削波（接近最大值的样本比例）
    clipping_threshold = 0.99
    clipped_samples = torch.sum(torch.abs(audio) > clipping_threshold).item()
    clipping_ratio = clipped_samples / audio.numel()

    # 检查静音（过低的信号）
    silence_threshold = 0.001
    is_mostly_silent = mean_val < silence_threshold

    quality_report = {
        "chunk_id": chunk_id,
        "peak_amplitude": max_val,
        "mean_amplitude": mean_val,
        "std_deviation": std_val,
        "has_nan": has_nan,
        "has_inf": has_inf,
        "clipping_ratio": clipping_ratio,
        "is_mostly_silent": is_mostly_silent,
        "sample_count": audio.numel()
    }

    # 质量警告
    warnings = []
    if max_val > 1.0:
        warnings.append(f"音频峰值超出范围: {max_val:.3f}")
    if clipping_ratio > 0.01:  # 超过1%的样本被削波
        warnings.append(f"检测到削波: {clipping_ratio*100:.1f}%的样本")
    if has_nan or has_inf:
        warnings.append("检测到NaN或Inf值")
    if is_mostly_silent:
        warnings.append(f"音频信号过弱: 平均幅度 {mean_val:.6f}")

    quality_report["warnings"] = warnings
    quality_report["is_healthy"] = len(warnings) == 0

    return quality_report


def analyze_audio_processing_chain(original_tensor: torch.Tensor, processed_bytes: bytes,
                                  dtype: str = 'int16', chunk_id: int = 0) -> dict:
    """
    分析音频处理链的效果，比较原始音频和处理后音频的质量

    Args:
        original_tensor: 原始音频tensor
        processed_bytes: 处理后的PCM字节数据
        dtype: PCM数据类型
        chunk_id: 音频块ID

    Returns:
        dict: 处理效果分析报告
    """
    # 将处理后的字节数据转换回tensor进行分析
    if dtype == 'int16':
        processed_array = np.frombuffer(processed_bytes, dtype=np.int16).astype(np.float32) / 32767.0
    elif dtype == 'int32':
        processed_array = np.frombuffer(processed_bytes, dtype=np.int32).astype(np.float32) / **********.0
    elif dtype == 'float32':
        processed_array = np.frombuffer(processed_bytes, dtype=np.float32)
    else:
        return {"error": f"不支持的数据类型: {dtype}"}

    processed_tensor = torch.from_numpy(processed_array).reshape(original_tensor.shape)

    # 分析原始音频
    orig_peak = torch.max(torch.abs(original_tensor.to(torch.float32))).item()
    orig_rms = torch.sqrt(torch.mean(original_tensor.to(torch.float32) ** 2)).item()

    # 分析处理后音频
    proc_peak = torch.max(torch.abs(processed_tensor)).item()
    proc_rms = torch.sqrt(torch.mean(processed_tensor ** 2)).item()

    # 计算处理效果
    peak_change_db = 20 * np.log10(proc_peak / orig_peak) if orig_peak > 0 else 0
    rms_change_db = 20 * np.log10(proc_rms / orig_rms) if orig_rms > 0 else 0

    analysis = {
        "chunk_id": chunk_id,
        "original": {
            "peak": orig_peak,
            "rms": orig_rms,
            "peak_db": 20 * np.log10(orig_peak) if orig_peak > 0 else -np.inf
        },
        "processed": {
            "peak": proc_peak,
            "rms": proc_rms,
            "peak_db": 20 * np.log10(proc_peak) if proc_peak > 0 else -np.inf
        },
        "changes": {
            "peak_change_db": peak_change_db,
            "rms_change_db": rms_change_db,
            "dynamic_range_preserved": abs(peak_change_db - rms_change_db) < 1.0  # 动态范围是否保持
        }
    }

    return analysis


class CosyVoiceGrpcServicer(CosyVoiceServiceServicer):

    def __init__(self):
        super().__init__()
        self.cosyvoice = CosyVoice2(
            model_dir='CosyVoice/pretrained_models/CosyVoice2-0.5B',
            load_jit=True,
            load_trt=True,
            fp16=True,
            load_vllm=True,
            trt_concurrent=4
        )

    def ModelPreHot(self):
        """模型预热"""
        request = InferenceZeroShotReq(role="172946", text="这是真爱吗，每晚面对一个头发花白满嘴白毛的糟老头，谁受得了。",
                                       speed_factor=1.0)
        # 检查示例音频
        ref_audio_path = f'ref_audio/{request.role}.wav'
        try:
            ref_audio = ref_audio_cached(ref_audio_path)
        except Exception as e:
            return InferenceResp(err_msg=str(e))
        # 检查示例音频文本内容
        ref_prompt_path = f'ref_audio/{request.role}.txt'
        try:
            ref_prompt = ref_text_cached(ref_prompt_path)
        except Exception as e:
            return InferenceResp(err_msg=str(e))
        log.debug(f"CosyVoice 准备推理:role:{request.role}, ref_prompt:{ref_prompt}, ref_audio_path:{ref_audio_path}")
        # 执行推理
        for i, audio_output in enumerate(self.cosyvoice.inference_zero_shot(request.text, ref_prompt, ref_audio, stream=True,
                                                                 speed=request.speed_factor)):
            log.info(f"模型预热:chunk_{i}, sample_rate:{self.cosyvoice.sample_rate}")
            # 预热时也检查音频质量
            if 'tts_speech' in audio_output:
                max_val = torch.max(torch.abs(audio_output['tts_speech']))
                log.debug(f"预热音频块 {i} 峰值: {max_val:.3f}")

    def InferenceZeroShot(self, request: InferenceZeroShotReq, context: grpc.ServicerContext):
        start = time.time()
        try:
            # 检查示例音频
            ref_audio_path = f'ref_audio/{request.role}.wav'
            try:
                ref_audio = ref_audio_cached(ref_audio_path)
            except Exception as e:
                return InferenceResp(err_msg=str(e))
            # 检查示例音频文本内容
            ref_prompt_path = f'ref_audio/{request.role}.txt'
            try:
                ref_prompt = ref_text_cached(ref_prompt_path)
            except Exception as e:
                return InferenceResp(err_msg=str(e))
            log.debug(
                f"CosyVoice 准备推理:role:{request.role}, req_text:{request.text}, ref_audio_path:{ref_audio_path}")

            # 执行推理
            for i, j in enumerate(self.cosyvoice.inference_zero_shot(request.text, ref_prompt, ref_audio, stream=True,
                                                                     speed=request.speed_factor)):
                if not context.is_active():
                    log.info("CosyVoice 推理中断，本次执行结束")
                    break

                # 检查原始音频质量
                quality_report = check_audio_quality(j['tts_speech'], chunk_id=i)
                if not quality_report["is_healthy"]:
                    log.warning(f"音频块 {i} 质量问题: {quality_report['warnings']}")

                # 专业音频处理：优先归一化，避免硬限幅失真
                audio_bytes = process_audio_to_pcm_bytes(
                    j['tts_speech'],
                    dtype='int16',
                    gain=1.0,
                    normalize=True,
                    enable_soft_limiting=True  # 启用软限制器
                )

                # 可选：分析音频处理效果（仅在debug级别日志时启用）
                if log.isEnabledFor(logging.DEBUG):
                    analysis = analyze_audio_processing_chain(j['tts_speech'], audio_bytes, 'int16', i)
                    log.debug(f"ZeroShot音频处理分析 块{i}: 峰值变化 {analysis['changes']['peak_change_db']:.1f}dB, "
                             f"RMS变化 {analysis['changes']['rms_change_db']:.1f}dB")

                yield InferenceResp(audio_data=audio_bytes, sr=self.cosyvoice.sample_rate)
        finally:
            end = time.time()
            log.info('CosyVoice 推理结束，耗时 {}'.format(end - start))

    def InferenceInstruct(self, request: InferenceInstructReq, context: grpc.ServicerContext):
        start = time.time()
        try:
            # 检查示例音频
            ref_audio_path = f'ref_audio/{request.role}.wav'
            try:
                ref_audio = ref_audio_cached(ref_audio_path)
            except Exception as e:
                return InferenceResp(err_msg=str(e))
            log.debug(
                f"CosyVoice Instruct 准备推理:role:{request.role}, req_text:{request.text}, instruct_text:{request.instruct_text}, ref_audio_path:{ref_audio_path}")

            # 执行推理
            for i, j in enumerate(
                    self.cosyvoice.inference_instruct2(request.text, request.instruct_text, ref_audio, stream=True,
                                                       speed=request.speed_factor)):
                if not context.is_active():
                    log.info("CosyVoice Instruct 推理中断，本次执行结束")
                    break

                # 检查原始音频质量
                quality_report = check_audio_quality(j['tts_speech'], chunk_id=i)
                if not quality_report["is_healthy"]:
                    log.warning(f"Instruct音频块 {i} 质量问题: {quality_report['warnings']}")

                # 专业音频处理：优先归一化，避免硬限幅失真
                audio_bytes = process_audio_to_pcm_bytes(
                    j['tts_speech'],
                    dtype='int16',
                    gain=1.0,
                    normalize=True,
                    enable_soft_limiting=True  # 启用软限制器
                )

                # 可选：分析音频处理效果（仅在debug级别日志时启用）
                if log.isEnabledFor(logging.DEBUG):
                    analysis = analyze_audio_processing_chain(j['tts_speech'], audio_bytes, 'int16', i)
                    log.debug(f"Instruct音频处理分析 块{i}: 峰值变化 {analysis['changes']['peak_change_db']:.1f}dB, "
                             f"RMS变化 {analysis['changes']['rms_change_db']:.1f}dB")

                yield InferenceResp(audio_data=audio_bytes, sr=self.cosyvoice.sample_rate)
        finally:
            end = time.time()
            log.info('CosyVoice Instruct 推理结束，耗时 {}'.format(end - start))
