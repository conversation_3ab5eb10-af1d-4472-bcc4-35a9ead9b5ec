#!/usr/bin/env python3
"""
音频处理测试脚本
用于验证新的音频处理函数是否正确工作，避免爆音和失真
"""

import torch
import numpy as np
import matplotlib.pyplot as plt
from grpc_handle.cosy_voice_grpc_handle import (
    process_audio_to_pcm_bytes, 
    check_audio_quality,
    analyze_audio_processing_chain
)

def create_test_signals():
    """创建各种测试信号"""
    sample_rate = 16000
    duration = 1.0  # 1秒
    t = torch.linspace(0, duration, int(sample_rate * duration))
    
    signals = {}
    
    # 1. 正常信号（峰值0.5）
    signals['normal'] = 0.5 * torch.sin(2 * np.pi * 440 * t)
    
    # 2. 过载信号（峰值1.5，会导致削波）
    signals['overload'] = 1.5 * torch.sin(2 * np.pi * 440 * t)
    
    # 3. 微弱信号（峰值0.01）
    signals['weak'] = 0.01 * torch.sin(2 * np.pi * 440 * t)
    
    # 4. 混合信号（包含多个频率）
    signals['complex'] = (0.3 * torch.sin(2 * np.pi * 440 * t) + 
                         0.2 * torch.sin(2 * np.pi * 880 * t) + 
                         0.1 * torch.sin(2 * np.pi * 1320 * t))
    
    # 5. 带噪声的信号
    noise = 0.05 * torch.randn_like(t)
    signals['noisy'] = 0.7 * torch.sin(2 * np.pi * 440 * t) + noise
    
    # 6. 极端信号（包含NaN和Inf）
    extreme = torch.sin(2 * np.pi * 440 * t)
    extreme[100:110] = float('nan')  # 添加NaN
    extreme[200:210] = float('inf')  # 添加Inf
    signals['extreme'] = extreme
    
    return signals

def test_audio_processing():
    """测试音频处理函数"""
    print("🎵 音频处理测试开始...")
    print("=" * 60)
    
    test_signals = create_test_signals()
    
    for signal_name, signal in test_signals.items():
        print(f"\n📊 测试信号: {signal_name}")
        print("-" * 40)
        
        # 检查原始信号质量
        quality_report = check_audio_quality(signal)
        print(f"原始信号质量:")
        print(f"  峰值: {quality_report['peak_amplitude']:.6f}")
        print(f"  平均幅度: {quality_report['mean_amplitude']:.6f}")
        print(f"  是否健康: {quality_report['is_healthy']}")
        if quality_report['warnings']:
            print(f"  警告: {quality_report['warnings']}")
        
        # 测试不同的处理参数
        test_configs = [
            {"name": "默认处理", "normalize": True, "gain": 1.0, "enable_soft_limiting": True},
            {"name": "无归一化", "normalize": False, "gain": 1.0, "enable_soft_limiting": True},
            {"name": "2倍增益", "normalize": True, "gain": 2.0, "enable_soft_limiting": True},
            {"name": "硬限幅", "normalize": True, "gain": 1.0, "enable_soft_limiting": False},
        ]
        
        for config in test_configs:
            try:
                # 处理音频
                processed_bytes = process_audio_to_pcm_bytes(
                    signal,
                    dtype='int16',
                    normalize=config['normalize'],
                    gain=config['gain'],
                    enable_soft_limiting=config['enable_soft_limiting']
                )
                
                # 分析处理效果
                analysis = analyze_audio_processing_chain(signal, processed_bytes, 'int16')
                
                print(f"  {config['name']}:")
                print(f"    峰值变化: {analysis['changes']['peak_change_db']:.1f} dB")
                print(f"    RMS变化: {analysis['changes']['rms_change_db']:.1f} dB")
                print(f"    动态范围保持: {analysis['changes']['dynamic_range_preserved']}")
                
            except Exception as e:
                print(f"  {config['name']}: ❌ 错误 - {e}")
    
    print("\n" + "=" * 60)
    print("✅ 音频处理测试完成!")

def test_extreme_cases():
    """测试极端情况"""
    print("\n🚨 极端情况测试...")
    print("=" * 60)
    
    # 测试空信号
    try:
        empty_signal = torch.zeros(1000)
        processed = process_audio_to_pcm_bytes(empty_signal)
        print("✅ 空信号处理成功")
    except Exception as e:
        print(f"❌ 空信号处理失败: {e}")
    
    # 测试单一值信号
    try:
        constant_signal = torch.ones(1000) * 0.5
        processed = process_audio_to_pcm_bytes(constant_signal)
        print("✅ 常数信号处理成功")
    except Exception as e:
        print(f"❌ 常数信号处理失败: {e}")
    
    # 测试非常大的信号
    try:
        huge_signal = torch.ones(1000) * 1000.0
        processed = process_audio_to_pcm_bytes(huge_signal)
        print("✅ 超大信号处理成功（应该被归一化）")
    except Exception as e:
        print(f"❌ 超大信号处理失败: {e}")

def benchmark_performance():
    """性能基准测试"""
    print("\n⚡ 性能基准测试...")
    print("=" * 60)
    
    import time
    
    # 创建较大的测试信号（10秒音频）
    sample_rate = 16000
    duration = 10.0
    t = torch.linspace(0, duration, int(sample_rate * duration))
    test_signal = torch.sin(2 * np.pi * 440 * t)
    
    # 测试处理时间
    iterations = 10
    start_time = time.time()
    
    for i in range(iterations):
        processed = process_audio_to_pcm_bytes(test_signal)
    
    end_time = time.time()
    avg_time = (end_time - start_time) / iterations
    
    print(f"信号长度: {len(test_signal)} 样本 ({duration}秒)")
    print(f"平均处理时间: {avg_time*1000:.2f} ms")
    print(f"实时倍数: {duration/avg_time:.1f}x")

if __name__ == "__main__":
    test_audio_processing()
    test_extreme_cases()
    benchmark_performance()
