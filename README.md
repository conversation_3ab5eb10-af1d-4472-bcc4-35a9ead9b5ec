# 基于cosyvoice的tts

### 初始化项目相关子模块
git submodule update --init --recursive

### 安装python依赖
```bash
python3.10 -m venv .venv

pip install -r CosyVoice/requirements.txt -i https://mirrors.aliyun.com/pypi/simple/ --trusted-host=mirrors.aliyun.com

pip install -r requirements.txt -i https://mirrors.aliyun.com/pypi/simple/ --trusted-host=mirrors.aliyun.com

# 修复冲突依赖
pip install numpy==1.26.4 -i https://mirrors.aliyun.com/pypi/simple/ --trusted-host=mirrors.aliyun.com
```

### 更新proto
```shell
make proto
```

### 启动grpc服务
```
python main.py
```

### 下载模型
python model_download.py
```python
# SDK模型下载
from modelscope import snapshot_download
snapshot_download('iic/CosyVoice2-0.5B', local_dir='CosyVoice/pretrained_models/CosyVoice2-0.5B')
snapshot_download('iic/CosyVoice-300M', local_dir='CosyVoice/pretrained_models/CosyVoice-300M')
snapshot_download('iic/CosyVoice-300M-SFT', local_dir='CosyVoice/pretrained_models/CosyVoice-300M-SFT')
snapshot_download('iic/CosyVoice-300M-Instruct', local_dir='CosyVoice/pretrained_models/CosyVoice-300M-Instruct')
snapshot_download('iic/CosyVoice-ttsfrd', local_dir='CosyVoice/pretrained_models/CosyVoice-ttsfrd')
```

### 启用frd
```shell
cd CosyVoice/pretrained_models/CosyVoice-ttsfrd/
unzip resource.zip -d .
pip install ttsfrd_dependency-0.1-py3-none-any.whl
pip install ttsfrd-0.4.2-cp310-cp310-linux_x86_64.whl
```

### service部署
- 修改`qt-tts-cosyvoice.service`中项目的根路径
- cp qt-tts-cosyvoice.service /etc/systemd/system/
- systemctl daemon-reload
- systemctl start qt-tts-cosyvoice

### 指导音色
指导音色和文本存放在ref_audio中